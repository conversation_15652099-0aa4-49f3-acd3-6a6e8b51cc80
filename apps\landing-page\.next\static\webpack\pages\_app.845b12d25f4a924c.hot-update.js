"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: var(--font-inter), Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: JetBrains Mono, monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  padding: 0;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n  html {\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n    background: var(--primary-bg);\\n    color: var(--text-primary);\\n}\\n  \\n  /* Enhanced Arabic font optimization */\\n  .font-arabic {\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n\\n  .font-cairo {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n  }\\n\\n  .font-tajawal {\\n    font-family: var(--font-tajawal), 'Tajawal', 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1;\\n  }\\n  \\n  /* RTL specific styles */\\n  [dir=\\\"rtl\\\"] {\\n    text-align: right;\\n  }\\n  \\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 8px;\\n  }\\n  \\n  ::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  :is(.dark *)::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  ::-webkit-scrollbar-thumb {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  :is(.dark *)::-webkit-scrollbar-thumb {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  ::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  :is(.dark *)::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.container {\\n  width: 100%;\\n}\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .container {\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\n.glass {\\n  background: rgba(255, 255, 255, 0.1);\\n  backdrop-filter: blur(20px);\\n  -webkit-backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease-in-out;\\n}\\n.glass:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-2px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.glass-button {\\n  background: rgba(255, 255, 255, 0.1);\\n  backdrop-filter: blur(20px);\\n  -webkit-backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  padding: 12px 24px;\\n  transition: all 0.3s ease-in-out;\\n  cursor: pointer;\\n}\\n.glass-button:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.glass-card {\\n  background: rgba(255, 255, 255, 0.08);\\n  backdrop-filter: blur(24px);\\n  -webkit-backdrop-filter: blur(24px);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease-in-out;\\n}\\n.glass-card:hover {\\n  background: rgba(255, 255, 255, 0.12);\\n  transform: translateY(-4px);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n}\\n/* Enhanced Button variants with glass effects */\\n.btn-primary {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-radius: 0.75rem;\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-primary:hover {\\n  --tw-translate-y: -0.25rem;\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  --tw-gradient-from: #0369a1 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(3 105 161 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: #075985 var(--tw-gradient-to-position);\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-primary {\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n.btn-secondary {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: rgb(255 255 255 / 0.2);\\n  background-color: rgb(255 255 255 / 0.1);\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-secondary:hover {\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-color: rgb(255 255 255 / 0.3);\\n  background-color: rgb(255 255 255 / 0.2);\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-secondary:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(56 189 248 / var(--tw-text-opacity, 1));\\n}\\n.btn-secondary {\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n  }\\n.btn-outline {\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: rgb(209 213 219 / 0.3);\\n  background-color: rgb(255 255 255 / 0.05);\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-outline:hover {\\n  border-color: rgb(156 163 175 / 0.5);\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\n.btn-outline:is(.dark *) {\\n  border-color: rgb(75 85 99 / 0.3);\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:hover:is(.dark *) {\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n.btn-outline {\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n  }\\n/* Enhanced Glass effect buttons */\\n.btn-glass {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-radius: 0.75rem;\\n  background-color: rgb(255 255 255 / 0.1);\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-glass:hover {\\n  --tw-translate-y: -0.25rem;\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.btn-glass {\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-glass:hover {\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n/* Enhanced glass button with premium effects */\\n/* Premium glass button for hero and main CTAs */\\n.glass-button {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1.5px solid rgba(255, 255, 255, 0.2);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    font-size: 1rem;\\n    font-weight: 600;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n    overflow: hidden;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n.glass-button::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, transparent 100%);\\n    transition: left 0.5s ease;\\n  }\\n.glass-button:hover::before {\\n    left: 100%;\\n  }\\n.glass-button:hover {\\n    transform: translateY(-2px) scale(1.02);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.10) 100%);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;\\n  }\\n/* Glass card for hero stats and feature cards */\\n.glass-card {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    border-radius: 16px;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.glass-card:hover {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    transform: translateY(-4px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n    border-color: rgba(255, 255, 255, 0.25);\\n  }\\n/* Enhanced Card styles with glass effects */\\n.card-premium {\\n  border-radius: 1.5rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 500ms;\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(30px);\\n    backdrop-filter: blur(30px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n.card-premium:hover {\\n    transform: translateY(-8px) scale(1.03);\\n    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n  }\\n/* Input styles */\\n/* Section spacing */\\n.section-padding {\\n  padding-top: 4rem;\\n  padding-bottom: 4rem;\\n}\\n@media (min-width: 1024px) {\\n\\n  .section-padding {\\n    padding-top: 6rem;\\n    padding-bottom: 6rem;\\n  }\\n}\\n.container-padding {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px) {\\n\\n  .container-padding {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container-padding {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n/* Enhanced Typography with premium styling - Dark theme optimized */\\n.heading-hero {\\n    font-size: clamp(3.5rem, 12vw, 8rem);\\n    font-weight: 900;\\n    line-height: 0.85;\\n    letter-spacing: -0.04em;\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n    font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);\\n    background-clip: text;\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\\n    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\\n  }\\n.heading-lg {\\n  font-family: var(--font-display), Poppins, system-ui, sans-serif;\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n  font-weight: 700;\\n  letter-spacing: -0.025em;\\n}\\n@media (min-width: 1024px) {\\n\\n  .heading-lg {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n}\\n.heading-md {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n  font-weight: 600;\\n  letter-spacing: -0.025em;\\n}\\n@media (min-width: 1024px) {\\n\\n  .heading-md {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n}\\n.heading-sm {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n  font-weight: 600;\\n}\\n@media (min-width: 1024px) {\\n\\n  .heading-sm {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\n/* Enhanced gradient text effects */\\n.gradient-text {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: rgb(217 70 239 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #d946ef var(--tw-gradient-via-position), var(--tw-gradient-to);\\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n  color: transparent;\\n    background-size: 200% 200%;\\n    animation: gradientShift 3s ease-in-out infinite;\\n}\\n/* Premium Gold/Metallic Text Effects */\\n.gradient-text-gold {\\n    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #ffb300 75%, #ffd700 100%);\\n    background-size: 400% 400%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: goldShine 3s ease-in-out infinite;\\n    text-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);\\n    filter: drop-shadow(0 2px 8px rgba(255, 215, 0, 0.3));\\n  }\\n.gradient-text-gold-premium {\\n    background: linear-gradient(135deg,\\n      #8B6914 0%,\\n      #B8860B 8%,\\n      #DAA520 16%,\\n      #FFD700 24%,\\n      #FFED4E 32%,\\n      #FFF8DC 40%,\\n      #FFED4E 48%,\\n      #FFD700 56%,\\n      #DAA520 64%,\\n      #B8860B 72%,\\n      #8B6914 80%,\\n      #B8860B 88%,\\n      #FFD700 96%,\\n      #FFED4E 100%);\\n    background-size: 800% 800%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGoldShine 5s ease-in-out infinite;\\n    text-shadow: 0 8px 20px rgba(255, 215, 0, 0.6);\\n    filter: drop-shadow(0 6px 16px rgba(255, 215, 0, 0.5)) drop-shadow(0 0 30px rgba(255, 215, 0, 0.3)) drop-shadow(0 2px 8px rgba(184, 134, 11, 0.4));\\n    position: relative;\\n  }\\n/* Enhanced Arabic typography */\\n.text-arabic {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n    font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n.text-arabic-premium {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n    font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n    color: var(--text-primary);\\n    text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);\\n    letter-spacing: 0.025em;\\n    line-height: 1.3;\\n    font-weight: 700;\\n  }\\n/* Premium section headers with Syrian cultural elements */\\n/* Gold accent elements */\\n/* Premium glass morphism cards */\\n/* Gold gradient buttons */\\n/* Shimmer effect for buttons and cards */\\n/* Popular badge styling */\\n/* Tab navigation premium styling */\\n/* Premium Gold/Metallic Buttons */\\n.btn-gold-premium {\\n    background: linear-gradient(135deg,\\n      #8B6914 0%,\\n      #B8860B 15%,\\n      #DAA520 30%,\\n      #FFD700 45%,\\n      #FFED4E 60%,\\n      #FFD700 75%,\\n      #DAA520 90%,\\n      #B8860B 100%);\\n    background-size: 300% 300%;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 2px solid rgba(255, 215, 0, 0.4);\\n    box-shadow:\\n      0 8px 25px rgba(255, 215, 0, 0.4),\\n      0 4px 15px rgba(184, 134, 11, 0.3),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.3),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.2);\\n    color: #1a1a1a;\\n    font-weight: 700;\\n    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.4);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n    animation: goldShine 4s ease-in-out infinite;\\n  }\\n.btn-gold-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);\\n    transition: left 0.6s ease;\\n  }\\n.btn-gold-premium:hover::before {\\n    left: 100%;\\n  }\\n.btn-gold-premium:hover {\\n    transform: translateY(-3px) scale(1.05);\\n    box-shadow:\\n      0 15px 40px rgba(255, 215, 0, 0.5),\\n      0 8px 25px rgba(184, 134, 11, 0.4),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.4),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.3);\\n    background-position: 100% 100%;\\n    filter: brightness(1.1) saturate(1.1);\\n    border-color: rgba(255, 215, 0, 0.6);\\n  }\\n/* Metallic Card Effects */\\n.card-metallic-gold {\\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 215, 0, 0.2);\\n    border-radius: 20px;\\n    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-metallic-gold::before {\\n    content: '';\\n    position: absolute;\\n    top: -50%;\\n    left: -50%;\\n    width: 200%;\\n    height: 200%;\\n    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);\\n    animation: goldGlow 4s ease-in-out infinite;\\n    pointer-events: none;\\n  }\\n.card-metallic-gold:hover {\\n    transform: translateY(-8px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n    border-color: rgba(255, 215, 0, 0.4);\\n  }\\n/* Animation utilities */\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.visible {\\n  visibility: visible;\\n}\\n.static {\\n  position: static;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.-inset-4 {\\n  inset: -1rem;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-right-2 {\\n  right: -0.5rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.-top-2 {\\n  top: -0.5rem;\\n}\\n.-top-6 {\\n  top: -1.5rem;\\n}\\n.bottom-0 {\\n  bottom: 0px;\\n}\\n.bottom-28 {\\n  bottom: 7rem;\\n}\\n.bottom-32 {\\n  bottom: 8rem;\\n}\\n.bottom-36 {\\n  bottom: 9rem;\\n}\\n.bottom-40 {\\n  bottom: 10rem;\\n}\\n.bottom-8 {\\n  bottom: 2rem;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\n.left-10 {\\n  left: 2.5rem;\\n}\\n.left-12 {\\n  left: 3rem;\\n}\\n.left-14 {\\n  left: 3.5rem;\\n}\\n.left-16 {\\n  left: 4rem;\\n}\\n.left-20 {\\n  left: 5rem;\\n}\\n.left-6 {\\n  left: 1.5rem;\\n}\\n.left-8 {\\n  left: 2rem;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-1 {\\n  right: 0.25rem;\\n}\\n.right-1\\\\/4 {\\n  right: 25%;\\n}\\n.right-10 {\\n  right: 2.5rem;\\n}\\n.right-12 {\\n  right: 3rem;\\n}\\n.right-16 {\\n  right: 4rem;\\n}\\n.right-20 {\\n  right: 5rem;\\n}\\n.right-4 {\\n  right: 1rem;\\n}\\n.right-6 {\\n  right: 1.5rem;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.top-1 {\\n  top: 0.25rem;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.top-20 {\\n  top: 5rem;\\n}\\n.top-24 {\\n  top: 6rem;\\n}\\n.top-28 {\\n  top: 7rem;\\n}\\n.top-32 {\\n  top: 8rem;\\n}\\n.top-4 {\\n  top: 1rem;\\n}\\n.top-40 {\\n  top: 10rem;\\n}\\n.top-6 {\\n  top: 1.5rem;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.-m-4 {\\n  margin: -1rem;\\n}\\n.-m-6 {\\n  margin: -1.5rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-10 {\\n  margin-bottom: 2.5rem;\\n}\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\n.mb-16 {\\n  margin-bottom: 4rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-20 {\\n  margin-bottom: 5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.me-4 {\\n  margin-inline-end: 1rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-20 {\\n  margin-top: 5rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-1 {\\n  height: 0.25rem;\\n}\\n.h-1\\\\.5 {\\n  height: 0.375rem;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-14 {\\n  height: 3.5rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-20 {\\n  height: 5rem;\\n}\\n.h-24 {\\n  height: 6rem;\\n}\\n.h-28 {\\n  height: 7rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-36 {\\n  height: 9rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-40 {\\n  height: 10rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-7 {\\n  height: 1.75rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-1 {\\n  width: 0.25rem;\\n}\\n.w-1\\\\.5 {\\n  width: 0.375rem;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-14 {\\n  width: 3.5rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-20 {\\n  width: 5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-28 {\\n  width: 7rem;\\n}\\n.w-32 {\\n  width: 8rem;\\n}\\n.w-36 {\\n  width: 9rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-40 {\\n  width: 10rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-7 {\\n  width: 1.75rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\n.max-w-6xl {\\n  max-width: 72rem;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-x-full {\\n  --tw-translate-x: -100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-skew-x-12 {\\n  --tw-skew-x: -12deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-x-0 {\\n  --tw-scale-x: 0;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.resize-none {\\n  resize: none;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.items-baseline {\\n  align-items: baseline;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-12 {\\n  gap: 3rem;\\n}\\n.gap-16 {\\n  gap: 4rem;\\n}\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\n.rounded-3xl {\\n  border-radius: 1.5rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\n.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.bg-gold-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gold-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(253 244 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-transparent {\\n  background-color: transparent;\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n.bg-white\\\\/95 {\\n  background-color: rgb(255 255 255 / 0.95);\\n}\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-gold-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-50 {\\n  --tw-gradient-from: #f0f9ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(240 249 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-500 {\\n  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(14 165 233 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-transparent {\\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.via-gold-600 {\\n  --tw-gradient-to: rgb(217 119 6 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #d97706 var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.via-white\\\\/20 {\\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.to-primary-500 {\\n  --tw-gradient-to: #0ea5e9 var(--tw-gradient-to-position);\\n}\\n.to-secondary-50 {\\n  --tw-gradient-to: #fdf4ff var(--tw-gradient-to-position);\\n}\\n.to-secondary-500 {\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-transparent {\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-10 {\\n  padding-left: 2.5rem;\\n  padding-right: 2.5rem;\\n}\\n.px-12 {\\n  padding-left: 3rem;\\n  padding-right: 3rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.text-start {\\n  text-align: start;\\n}\\n.font-cairo {\\n  font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n}\\n.font-display {\\n  font-family: var(--font-display), Poppins, system-ui, sans-serif;\\n}\\n.font-sans {\\n  font-family: var(--font-inter), Inter, system-ui, sans-serif;\\n}\\n.font-tajawal {\\n  font-family: var(--font-tajawal), Tajawal, Cairo, Noto Sans Arabic, system-ui, sans-serif;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-5xl {\\n  font-size: 3rem;\\n  line-height: 1;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.font-black {\\n  font-weight: 900;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-success-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-white\\\\/50 {\\n  color: rgb(255 255 255 / 0.5);\\n}\\n.text-white\\\\/60 {\\n  color: rgb(255 255 255 / 0.6);\\n}\\n.text-white\\\\/70 {\\n  color: rgb(255 255 255 / 0.7);\\n}\\n.text-white\\\\/80 {\\n  color: rgb(255 255 255 / 0.8);\\n}\\n.text-white\\\\/90 {\\n  color: rgb(255 255 255 / 0.9);\\n}\\n.text-white\\\\/95 {\\n  color: rgb(255 255 255 / 0.95);\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.decoration-white\\\\/30 {\\n  text-decoration-color: rgb(255 255 255 / 0.3);\\n}\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\n.antialiased {\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-10 {\\n  opacity: 0.1;\\n}\\n.opacity-15 {\\n  opacity: 0.15;\\n}\\n.opacity-20 {\\n  opacity: 0.2;\\n}\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\n.opacity-30 {\\n  opacity: 0.3;\\n}\\n.opacity-40 {\\n  opacity: 0.4;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow {\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow-lg {\\n  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-md {\\n  --tw-backdrop-blur: blur(12px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-1000 {\\n  transition-duration: 1000ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.rtl {\\n  direction: rtl;\\n}\\n.ltr {\\n  direction: ltr;\\n}\\n/* Text direction utilities */\\n.text-start {\\n    text-align: start;\\n  }\\n/* Margin utilities for RTL */\\n/* Padding utilities for RTL */\\n\\n/* CSS Variables for theming - Dark theme as default */\\n:root {\\n  /* Dark theme as default */\\n  --primary-bg: #0f172a;\\n  --secondary-bg: #1e293b;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --glass-bg: rgba(255, 255, 255, 0.1);\\n  --glass-border: rgba(255, 255, 255, 0.2);\\n  --toast-bg: #1f2937;\\n  --toast-color: #f9fafb;\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --primary-bg: #ffffff;\\n  --secondary-bg: #f8fafc;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #1f2937;\\n  --text-secondary: #64748b;\\n  --glass-bg: rgba(255, 255, 255, 0.8);\\n  --glass-border: rgba(0, 0, 0, 0.1);\\n  --toast-bg: #ffffff;\\n  --toast-color: #1f2937;\\n}\\n\\n/* Base styles */\\n\\n/* Component styles */\\n\\n/* Utility classes */\\n\\n/* Keyframe animations */\\n@keyframes fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInLeft {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes fadeInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n/* Enhanced keyframe animations for glass effects */\\n@keyframes gradientShift {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGradient {\\n  0%, 100% { background-position: 0% 0%; }\\n  25% { background-position: 100% 0%; }\\n  50% { background-position: 100% 100%; }\\n  75% { background-position: 0% 100%; }\\n}\\n\\n@keyframes glassShimmer {\\n  0% { background-position: -200% 0; }\\n  100% { background-position: 200% 0; }\\n}\\n\\n@keyframes floatGlass {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n    filter: blur(0px);\\n  }\\n  25% {\\n    transform: translateY(-10px) rotate(1deg);\\n    filter: blur(0.5px);\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(0deg);\\n    filter: blur(1px);\\n  }\\n  75% {\\n    transform: translateY(-10px) rotate(-1deg);\\n    filter: blur(0.5px);\\n  }\\n}\\n\\n@keyframes pulseGlass {\\n  0%, 100% {\\n    background: rgba(255, 255, 255, 0.1);\\n    border-color: rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n  50% {\\n    background: rgba(255, 255, 255, 0.15);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n/* Premium Gold/Metallic Animations */\\n@keyframes goldShine {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGoldShine {\\n  0% { background-position: -200% 0%; }\\n  50% { background-position: 0% 0%; }\\n  100% { background-position: 200% 0%; }\\n}\\n\\n@keyframes goldGlimmer {\\n  0% { transform: translateX(-100%) skewX(-15deg); }\\n  100% { transform: translateX(200%) skewX(-15deg); }\\n}\\n\\n@keyframes textShimmer {\\n  0% {\\n    background-position: -200% 0;\\n    opacity: 0;\\n  }\\n  50% {\\n    background-position: 0% 0;\\n    opacity: 1;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n    opacity: 0;\\n  }\\n}\\n\\n@keyframes textShimmerEnhanced {\\n  0% {\\n    background-position: -200% 0;\\n    opacity: 0;\\n  }\\n  10% {\\n    opacity: 0.3;\\n  }\\n  50% {\\n    background-position: 0% 0;\\n    opacity: 1;\\n  }\\n  90% {\\n    opacity: 0.3;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n    opacity: 0;\\n  }\\n}\\n\\n@keyframes goldGlow {\\n  0%, 100% {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: rotate(180deg) scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n\\n/* Reduced motion preferences */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n\\n  .glass,\\n  .glass-card,\\n  .glass-button {\\n    background: white !important;\\n    backdrop-filter: none !important;\\n    -webkit-backdrop-filter: none !important;\\n    border: 1px solid #ccc !important;\\n  }\\n}\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n.hover\\\\:text-primary-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-primary-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-white\\\\/50:focus {\\n  --tw-ring-color: rgb(255 255 255 / 0.5);\\n}\\n.group:hover .group-hover\\\\:translate-x-full {\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:rotate-12 {\\n  --tw-rotate: 12deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-110 {\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-x-100 {\\n  --tw-scale-x: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n.group:hover .group-hover\\\\:opacity-30 {\\n  opacity: 0.3;\\n}\\n.dark\\\\:border-gray-600:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:border-gray-700:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:bg-gray-600:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.dark\\\\:bg-gray-900:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\n.dark\\\\:bg-gray-900\\\\/95:is(.dark *) {\\n  background-color: rgb(17 24 39 / 0.95);\\n}\\n.dark\\\\:bg-primary-900\\\\/20:is(.dark *) {\\n  background-color: rgb(12 74 110 / 0.2);\\n}\\n.dark\\\\:bg-secondary-900\\\\/20:is(.dark *) {\\n  background-color: rgb(112 26 117 / 0.2);\\n}\\n.dark\\\\:from-gray-800:is(.dark *) {\\n  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.dark\\\\:to-gray-700:is(.dark *) {\\n  --tw-gradient-to: #374151 var(--tw-gradient-to-position);\\n}\\n.dark\\\\:text-gray-300:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:text-gray-400:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:text-gray-600:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:hover\\\\:bg-gray-800:hover:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n.dark\\\\:hover\\\\:text-primary-400:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(56 189 248 / var(--tw-text-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:h-10 {\\n    height: 2.5rem;\\n  }\\n\\n  .lg\\\\:h-20 {\\n    height: 5rem;\\n  }\\n\\n  .lg\\\\:h-6 {\\n    height: 1.5rem;\\n  }\\n\\n  .lg\\\\:w-10 {\\n    width: 2.5rem;\\n  }\\n\\n  .lg\\\\:w-6 {\\n    width: 1.5rem;\\n  }\\n\\n  .lg\\\\:scale-110 {\\n    --tw-scale-x: 1.1;\\n    --tw-scale-y: 1.1;\\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:p-12 {\\n    padding: 3rem;\\n  }\\n\\n  .lg\\\\:p-16 {\\n    padding: 4rem;\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .lg\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .lg\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .lg\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .lg\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .xl\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\\n.rtl\\\\:space-x-reverse:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,4DAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,sCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,mPAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,UAAc;EAAd,iCAAc;UAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;KAAd,sBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,sQAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,oKAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,uOAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,uBAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd,yDAAc;EAAd,oBAAc;EAAd,mDAAc;EAAd,+FAAc;EAAd,wDAAc;EAAd,0BAAc;IAAd,6BAAc;IAAd;AAAc;;EAAd,sCAAc;EAAd;IAAA,uEAAc;IAAd,mDAAc;IAAd,kCAAc;EAAA;;EAAd;IAAA,6FAAc;IAAd,6DAAc;EAAA;;EAAd;IAAA,+FAAc;IAAd,mDAAc;EAAA;;EAAd,wBAAc;EAAd;IAAA,iBAAc;EAAA;;EAAd,qBAAc;EAAd;IAAA,UAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,qBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;EAAA,oCAAoB;EAApB,2BAAoB;EAApB,mCAAoB;EAApB,0CAAoB;EAApB,yCAAoB;EAApB;AAAoB;AAApB;EAAA,qCAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,oCAAoB;EAApB,2BAAoB;EAApB,mCAAoB;EAApB,0CAAoB;EAApB,mBAAoB;EAApB,kBAAoB;EAApB,gCAAoB;EAApB;AAAoB;AAApB;EAAA,oCAAoB;EAApB,uCAAoB;EAApB;AAAoB;AAApB;EAAA,qCAAoB;EAApB,2BAAoB;EAApB,mCAAoB;EAApB,2CAAoB;EAApB,mBAAoB;EAApB,aAAoB;EAApB,yCAAoB;EAApB;AAAoB;AAApB;EAAA,qCAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAwFlB,gDAAgD;AAE9C;EAAA,+LAAsP;EAAtP,sBAAsP;EAAtP,qEAAsP;EAAtP,4DAAsP;EAAtP,mEAAsP;EAAtP,mEAAsP;EAAtP,wDAAsP;EAAtP,kBAAsP;EAAtP,mBAAsP;EAAtP,iBAAsP;EAAtP,oBAAsP;EAAtP,gBAAsP;EAAtP,oBAAsP;EAAtP,mDAAsP;EAAtP,+EAAsP;EAAtP,mGAAsP;EAAtP,uGAAsP;EAAtP,wBAAsP;EAAtP,wDAAsP;EAAtP;AAAsP;AAAtP;EAAA,0BAAsP;EAAtP,kBAAsP;EAAtP,kBAAsP;EAAtP,+LAAsP;EAAtP,4DAAsP;EAAtP,mEAAsP;EAAtP,mEAAsP;EAAtP,wDAAsP;EAAtP,gFAAsP;EAAtP,oGAAsP;EAAtP;AAAsP;AADxP;IAEE,mCAAmC;IACnC,2BAA2B;IAC3B,0CAA0C;EAC5C;AAGE;EAAA,+LAAgP;EAAhP,sBAAgP;EAAhP,iBAAgP;EAAhP,oCAAgP;EAAhP,wCAAgP;EAAhP,kBAAgP;EAAhP,mBAAgP;EAAhP,iBAAgP;EAAhP,oBAAgP;EAAhP,gBAAgP;EAAhP,oBAAgP;EAAhP,iDAAgP;EAAhP,6EAAgP;EAAhP,iGAAgP;EAAhP,uGAAgP;EAAhP,wBAAgP;EAAhP,wDAAgP;EAAhP;AAAgP;AAAhP;EAAA,0BAAgP;EAAhP,+LAAgP;EAAhP,oCAAgP;EAAhP,wCAAgP;EAAhP,+EAAgP;EAAhP,mGAAgP;EAAhP;AAAgP;AAAhP;EAAA,oBAAgP;EAAhP;AAAgP;AADlP;IAEE,mCAAmC;IACnC,2BAA2B;EAC7B;AAGE;EAAA,sBAA0O;EAA1O,iBAA0O;EAA1O,oCAA0O;EAA1O,yCAA0O;EAA1O,oBAA0O;EAA1O,qBAA0O;EAA1O,oBAA0O;EAA1O,uBAA0O;EAA1O,gBAA0O;EAA1O,oBAA0O;EAA1O,gDAA0O;EAA1O,wBAA0O;EAA1O,wDAA0O;EAA1O;AAA0O;AAA1O;EAAA,oCAA0O;EAA1O;AAA0O;AAA1O;EAAA,iCAA0O;EAA1O,oBAA0O;EAA1O;AAA0O;AAA1O;EAAA;AAA0O;AAD5O;IAEE,mCAAmC;IACnC,2BAA2B;EAC7B;AAEA,kCAAkC;AAEhC;EAAA,+LAA6J;EAA7J,sBAA6J;EAA7J,wCAA6J;EAA7J,kBAA6J;EAA7J,mBAA6J;EAA7J,iBAA6J;EAA7J,oBAA6J;EAA7J,gBAA6J;EAA7J,oBAA6J;EAA7J,mDAA6J;EAA7J,wBAA6J;EAA7J,wDAA6J;EAA7J;AAA6J;AAA7J;EAAA,0BAA6J;EAA7J,kBAA6J;EAA7J,kBAA6J;EAA7J,+LAA6J;EAA7J;AAA6J;AAD/J;IAEE,mCAAmC;IACnC,2BAA2B;IAC3B,0CAA0C;IAC1C,yCAAyC;EAC3C;AAEA;IACE,2CAA2C;EAC7C;AAEA,+CAA+C;AAqC/C,gDAAgD;AAChD;IACE,iGAAiG;IACjG,mCAAmC;IACnC,2BAA2B;IAC3B,4CAA4C;IAC5C,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,iDAAiD;IACjD,kBAAkB;IAClB,gBAAgB;IAChB,yCAAyC;EAC3C;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,mGAAmG;IACnG,0BAA0B;EAC5B;AAEA;IACE,UAAU;EACZ;AAEA;IACE,uCAAuC;IACvC,iGAAiG;IACjG,sCAAsC;IACtC,qFAAqF;EACvF;AAEA,gDAAgD;AAChD;IACE,iGAAiG;IACjG,mCAAmC;IACnC,2BAA2B;IAC3B,2CAA2C;IAC3C,mBAAmB;IACnB,yCAAyC;IACzC,iDAAiD;EACnD;AAEA;IACE,iGAAiG;IACjG,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;EACzC;AAEA,4CAA4C;AA2B1C;EAAA,qBAA8C;EAA9C,wBAA8C;EAA9C,wDAA8C;EAA9C,0BAA8C;IAC9C,gGAAgG;IAChG,mCAAmC;IACnC,2BAA2B;IAC3B,0CAA0C;IAC1C;AAL8C;AAQhD;IACE,uCAAuC;IACvC,mFAAmF;EACrF;AAEA,iBAAiB;AAKjB,oBAAoB;AAElB;EAAA,iBAAqB;EAArB;AAAqB;AAArB;;EAAA;IAAA,iBAAqB;IAArB;EAAqB;AAAA;AAIrB;EAAA,kBAA2B;EAA3B;AAA2B;AAA3B;;EAAA;IAAA,oBAA2B;IAA3B;EAA2B;AAAA;AAA3B;;EAAA;IAAA,kBAA2B;IAA3B;EAA2B;AAAA;AAG7B,oEAAoE;AACpE;IACE,oCAAoC;IACpC,gBAAgB;IAChB,iBAAiB;IACjB,uBAAuB;IACvB,6FAAiB;IAAjB,6DAAiB;IAAjB,uFAAiB;IACjB,0EAA0E;IAC1E,qBAAqB;IACrB,6BAA6B;IAC7B,oCAAoC;IACpC,0CAA0C;IAC1C,iDAAiD;EACnD;AAeE;EAAA,gEAAiE;EAAjE,mBAAiE;EAAjE,oBAAiE;EAAjE,gBAAiE;EAAjE;AAAiE;AAAjE;;EAAA;IAAA,kBAAiE;IAAjE;EAAiE;AAAA;AAIjE;EAAA,iBAAwD;EAAxD,iBAAwD;EAAxD,gBAAwD;EAAxD;AAAwD;AAAxD;;EAAA;IAAA,mBAAwD;IAAxD;EAAwD;AAAA;AAIxD;EAAA,kBAAwC;EAAxC,oBAAwC;EAAxC;AAAwC;AAAxC;;EAAA;IAAA,iBAAwC;IAAxC;EAAwC;AAAA;AAG1C,mCAAmC;AAEjC;EAAA,qEAAuG;EAAvG,4DAAuG;EAAvG,mEAAuG;EAAvG,mEAAuG;EAAvG,qEAAuG;EAAvG,4GAAuG;EAAvG,wDAAuG;EAAvG,6BAAuG;UAAvG,qBAAuG;EAAvG,kBAAuG;IACvG,0BAA0B;IAC1B;AAFuG;AAezG,uCAAuC;AACvC;IACE,oGAAoG;IACpG,0BAA0B;IAC1B,6BAA6B;IAC7B,qBAAqB;IACrB,oCAAoC;IACpC,4CAA4C;IAC5C,8CAA8C;IAC9C,qDAAqD;EACvD;AAEA;IACE;;;;;;;;;;;;;;mBAce;IACf,0BAA0B;IAC1B,6BAA6B;IAC7B,qBAAqB;IACrB,oCAAoC;IACpC,mDAAmD;IACnD,8CAA8C;IAC9C,kJAAkJ;IAClJ,kBAAkB;EACpB;AASA,+BAA+B;AAE7B;IAAA,6FAAiB;IAAjB,6DAAiB;IAAjB,uFAAiB;IACjB,uEAAuE;IACvE,mDAAmD;IACnD,kCAAkC;EAHjB;AAajB;IAAA,6FAAiB;IAAjB,6DAAiB;IAAjB,uFAAiB;IACjB,uEAAuE;IACvE,mDAAmD;IACnD,kCAAkC;IAClC,0BAA0B;IAC1B,6CAA6C;IAC7C,uBAAuB;IACvB,gBAAgB;IAChB,gBAAgB;EARC;AAWnB,0DAA0D;AAU1D,yBAAyB;AAOzB,iCAAiC;AAiBjC,0BAA0B;AAmB1B,yCAAyC;AAMzC,0BAA0B;AAQ1B,mCAAmC;AAUnC,kCAAkC;AAClC;IACE;;;;;;;;mBAQe;IACf,0BAA0B;IAC1B,mCAAmC;IACnC,2BAA2B;IAC3B,wCAAwC;IACxC;;;;4CAIwC;IACxC,cAAc;IACd,gBAAgB;IAChB,+CAA+C;IAC/C,mBAAmB;IACnB,kBAAkB;IAClB,gCAAgC;IAChC,kBAAkB;IAClB,gBAAgB;IAChB,4CAA4C;EAC9C;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,kGAAkG;IAClG,0BAA0B;EAC5B;AAEA;IACE,UAAU;EACZ;AAEA;IACE,uCAAuC;IACvC;;;;4CAIwC;IACxC,8BAA8B;IAC9B,qCAAqC;IACrC,oCAAoC;EACtC;AAEA,0BAA0B;AAC1B;IACE,4FAA4F;IAC5F,mCAAmC;IACnC,2BAA2B;IAC3B,wCAAwC;IACxC,mBAAmB;IACnB,qFAAqF;IACrF,gCAAgC;IAChC,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,+EAA+E;IAC/E,2CAA2C;IAC3C,oBAAoB;EACtB;AAEA;IACE,uCAAuC;IACvC,sFAAsF;IACtF,oCAAoC;EACtC;AAEA,wBAAwB;AAhjB1B;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,gEAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mCAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAokBjB,6BAA6B;AAC7B;IACE,iBAAiB;EACnB;AAMA,6BAA6B;AAS7B,8BAA8B;;AAplBhC,sDAAsD;AACtD;EACE,0BAA0B;EAC1B,qBAAqB;EACrB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,yBAAyB;EACzB,oCAAoC;EACpC,wCAAwC;EACxC,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA;EACE,qBAAqB;EACrB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,yBAAyB;EACzB,oCAAoC;EACpC,kCAAkC;EAClC,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA,gBAAgB;;AAyDhB,qBAAqB;;AA6erB,oBAAoB;;AA8BpB,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,4BAA4B;EAC9B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,4BAA4B;EAC9B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA,mDAAmD;AACnD;EACE,WAAW,2BAA2B,EAAE;EACxC,MAAM,6BAA6B,EAAE;AACvC;;AAEA;EACE,WAAW,0BAA0B,EAAE;EACvC,MAAM,4BAA4B,EAAE;EACpC,MAAM,8BAA8B,EAAE;EACtC,MAAM,4BAA4B,EAAE;AACtC;;AAEA;EACE,KAAK,4BAA4B,EAAE;EACnC,OAAO,2BAA2B,EAAE;AACtC;;AAEA;EACE;IACE,uCAAuC;IACvC,iBAAiB;EACnB;EACA;IACE,yCAAyC;IACzC,mBAAmB;EACrB;EACA;IACE,yCAAyC;IACzC,iBAAiB;EACnB;EACA;IACE,0CAA0C;IAC1C,mBAAmB;EACrB;AACF;;AAEA;EACE;IACE,oCAAoC;IACpC,sCAAsC;IACtC,yCAAyC;EAC3C;EACA;IACE,qCAAqC;IACrC,sCAAsC;IACtC,2CAA2C;EAC7C;AACF;;AAEA,qCAAqC;AACrC;EACE,WAAW,2BAA2B,EAAE;EACxC,MAAM,6BAA6B,EAAE;AACvC;;AAEA;EACE,KAAK,6BAA6B,EAAE;EACpC,MAAM,0BAA0B,EAAE;EAClC,OAAO,4BAA4B,EAAE;AACvC;;AAEA;EACE,KAAK,0CAA0C,EAAE;EACjD,OAAO,yCAAyC,EAAE;AACpD;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,yBAAyB;IACzB,UAAU;EACZ;EACA;IACE,2BAA2B;IAC3B,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,YAAY;EACd;EACA;IACE,yBAAyB;IACzB,UAAU;EACZ;EACA;IACE,YAAY;EACd;EACA;IACE,2BAA2B;IAC3B,UAAU;EACZ;AACF;;AAEA;EACE;IACE,gCAAgC;IAChC,YAAY;EACd;EACA;IACE,oCAAoC;IACpC,YAAY;EACd;AACF;;AAEA,+BAA+B;AAC/B;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAEA;;;IAGE,4BAA4B;IAC5B,gCAAgC;IAChC,wCAAwC;IACxC,iCAAiC;EACnC;AACF;AA3xBA;EAAA,kBA4xBA;EA5xBA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA,8BA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,2GA4xBA;EA5xBA,yGA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA,sBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,kBA4xBA;EA5xBA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,iBA4xBA;EA5xBA,iBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,eA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA,sBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,sBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA,4DA4xBA;EA5xBA,kEA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,kBA4xBA;EA5xBA;AA4xBA;AA5xBA;EAAA,oBA4xBA;EA5xBA;AA4xBA;AA5xBA;;EAAA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA,oBA4xBA;IA5xBA;EA4xBA;AAAA;AA5xBA;;EAAA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;AAAA;AA5xBA;;EAAA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA,iBA4xBA;IA5xBA,iBA4xBA;IA5xBA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA;EA4xBA;;EA5xBA;IAAA,kBA4xBA;IA5xBA;EA4xBA;;EA5xBA;IAAA,iBA4xBA;IA5xBA;EA4xBA;;EA5xBA;IAAA,iBA4xBA;IA5xBA;EA4xBA;;EA5xBA;IAAA,mBA4xBA;IA5xBA;EA4xBA;;EA5xBA;IAAA,eA4xBA;IA5xBA;EA4xBA;;EA5xBA;IAAA,kBA4xBA;IA5xBA;EA4xBA;AAAA;AA5xBA;;EAAA;IAAA,kBA4xBA;IA5xBA;EA4xBA;AAAA;AA5xBA;EAAA;AA4xBA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* CSS Variables for theming - Dark theme as default */\\n:root {\\n  /* Dark theme as default */\\n  --primary-bg: #0f172a;\\n  --secondary-bg: #1e293b;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --glass-bg: rgba(255, 255, 255, 0.1);\\n  --glass-border: rgba(255, 255, 255, 0.2);\\n  --toast-bg: #1f2937;\\n  --toast-color: #f9fafb;\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --primary-bg: #ffffff;\\n  --secondary-bg: #f8fafc;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #1f2937;\\n  --text-secondary: #64748b;\\n  --glass-bg: rgba(255, 255, 255, 0.8);\\n  --glass-border: rgba(0, 0, 0, 0.1);\\n  --toast-bg: #ffffff;\\n  --toast-color: #1f2937;\\n}\\n\\n/* Base styles */\\n@layer base {\\n  html {\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    @apply bg-dark-900 text-gray-100 transition-colors duration-300;\\n    background: var(--primary-bg);\\n    color: var(--text-primary);\\n  }\\n  \\n  /* Enhanced Arabic font optimization */\\n  .font-arabic {\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n\\n  .font-cairo {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n  }\\n\\n  .font-tajawal {\\n    font-family: var(--font-tajawal), 'Tajawal', 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1;\\n  }\\n  \\n  /* RTL specific styles */\\n  [dir=\\\"rtl\\\"] {\\n    text-align: right;\\n  }\\n  \\n  [dir=\\\"rtl\\\"] .ltr-content {\\n    direction: ltr;\\n    text-align: left;\\n  }\\n  \\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 8px;\\n  }\\n  \\n  ::-webkit-scrollbar-track {\\n    @apply bg-gray-100 dark:bg-gray-800;\\n  }\\n  \\n  ::-webkit-scrollbar-thumb {\\n    @apply bg-gray-300 dark:bg-gray-600 rounded-full;\\n  }\\n  \\n  ::-webkit-scrollbar-thumb:hover {\\n    @apply bg-gray-400 dark:bg-gray-500;\\n  }\\n}\\n\\n/* Component styles */\\n@layer components {\\n  /* Enhanced Button variants with glass effects */\\n  .btn-primary {\\n    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .btn-secondary {\\n    @apply bg-white/10 hover:bg-white/20 text-primary-600 dark:text-primary-400 border border-white/20 hover:border-white/30 font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n  }\\n\\n  .btn-outline {\\n    @apply border border-gray-300/30 hover:border-gray-400/50 text-gray-700 dark:text-gray-300 dark:border-gray-600/30 dark:hover:border-gray-500/50 font-medium px-6 py-3 rounded-xl transition-all duration-300 bg-white/5 hover:bg-white/10;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n  }\\n\\n  /* Enhanced Glass effect buttons */\\n  .btn-glass {\\n    @apply bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-glass:hover {\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n\\n  /* Enhanced glass button with premium effects */\\n  .glass-button-enhanced {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);\\n    -webkit-backdrop-filter: blur(32px);\\n    backdrop-filter: blur(32px);\\n    border: 2px solid rgba(255, 255, 255, 0.25);\\n    border-radius: 16px;\\n    padding: 1.25rem 2.5rem;\\n    font-size: 1.125rem;\\n    font-weight: 600;\\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .glass-button-enhanced::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n    transition: left 0.6s ease;\\n  }\\n\\n  .glass-button-enhanced:hover::before {\\n    left: 100%;\\n  }\\n\\n  .glass-button-enhanced:hover {\\n    transform: translateY(-3px) scale(1.03);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);\\n    border-color: rgba(255, 255, 255, 0.4);\\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;\\n  }\\n\\n  /* Premium glass button for hero and main CTAs */\\n  .glass-button {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1.5px solid rgba(255, 255, 255, 0.2);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    font-size: 1rem;\\n    font-weight: 600;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n    overflow: hidden;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .glass-button::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, transparent 100%);\\n    transition: left 0.5s ease;\\n  }\\n\\n  .glass-button:hover::before {\\n    left: 100%;\\n  }\\n\\n  .glass-button:hover {\\n    transform: translateY(-2px) scale(1.02);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.10) 100%);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;\\n  }\\n\\n  /* Glass card for hero stats and feature cards */\\n  .glass-card {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    border-radius: 16px;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .glass-card:hover {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    transform: translateY(-4px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n    border-color: rgba(255, 255, 255, 0.25);\\n  }\\n  \\n  /* Enhanced Card styles with glass effects */\\n  .card {\\n    @apply bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg border border-white/20 dark:border-gray-700/30 transition-all duration-300;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n  }\\n\\n  .card-hover {\\n    @apply hover:shadow-xl hover:-translate-y-2 transform transition-all duration-300 hover:scale-105;\\n  }\\n\\n  .card-glass {\\n    @apply rounded-2xl transition-all duration-300;\\n    background: rgba(255, 255, 255, 0.08);\\n    -webkit-backdrop-filter: blur(24px);\\n    backdrop-filter: blur(24px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .card-glass:hover {\\n    background: rgba(255, 255, 255, 0.12);\\n    transform: translateY(-4px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  }\\n\\n  .card-premium {\\n    @apply rounded-3xl transition-all duration-500;\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(30px);\\n    backdrop-filter: blur(30px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .card-premium:hover {\\n    transform: translateY(-8px) scale(1.03);\\n    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n  }\\n  \\n  /* Input styles */\\n  .input-field {\\n    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-all duration-200;\\n  }\\n  \\n  /* Section spacing */\\n  .section-padding {\\n    @apply py-16 lg:py-24;\\n  }\\n  \\n  .container-padding {\\n    @apply px-4 sm:px-6 lg:px-8;\\n  }\\n  \\n  /* Enhanced Typography with premium styling - Dark theme optimized */\\n  .heading-hero {\\n    font-size: clamp(3.5rem, 12vw, 8rem);\\n    font-weight: 900;\\n    line-height: 0.85;\\n    letter-spacing: -0.04em;\\n    @apply font-cairo;\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);\\n    background-clip: text;\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\\n    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\\n  }\\n\\n  .heading-hero-mobile {\\n    font-size: clamp(2.5rem, 15vw, 4rem);\\n    font-weight: 800;\\n    line-height: 0.9;\\n    letter-spacing: -0.03em;\\n    @apply font-cairo;\\n  }\\n\\n  .heading-xl {\\n    @apply text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight font-display;\\n  }\\n\\n  .heading-lg {\\n    @apply text-3xl lg:text-4xl font-bold tracking-tight font-display;\\n  }\\n\\n  .heading-md {\\n    @apply text-2xl lg:text-3xl font-semibold tracking-tight;\\n  }\\n\\n  .heading-sm {\\n    @apply text-xl lg:text-2xl font-semibold;\\n  }\\n\\n  /* Enhanced gradient text effects */\\n  .gradient-text {\\n    @apply bg-gradient-to-r from-primary-600 via-secondary-500 to-primary-700 bg-clip-text text-transparent;\\n    background-size: 200% 200%;\\n    animation: gradientShift 3s ease-in-out infinite;\\n  }\\n\\n  .gradient-text-premium {\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%);\\n    background-size: 300% 300%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGradient 4s ease-in-out infinite;\\n    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\\n  }\\n\\n  /* Premium Gold/Metallic Text Effects */\\n  .gradient-text-gold {\\n    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #ffb300 75%, #ffd700 100%);\\n    background-size: 400% 400%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: goldShine 3s ease-in-out infinite;\\n    text-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);\\n    filter: drop-shadow(0 2px 8px rgba(255, 215, 0, 0.3));\\n  }\\n\\n  .gradient-text-gold-premium {\\n    background: linear-gradient(135deg,\\n      #8B6914 0%,\\n      #B8860B 8%,\\n      #DAA520 16%,\\n      #FFD700 24%,\\n      #FFED4E 32%,\\n      #FFF8DC 40%,\\n      #FFED4E 48%,\\n      #FFD700 56%,\\n      #DAA520 64%,\\n      #B8860B 72%,\\n      #8B6914 80%,\\n      #B8860B 88%,\\n      #FFD700 96%,\\n      #FFED4E 100%);\\n    background-size: 800% 800%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGoldShine 5s ease-in-out infinite;\\n    text-shadow: 0 8px 20px rgba(255, 215, 0, 0.6);\\n    filter: drop-shadow(0 6px 16px rgba(255, 215, 0, 0.5)) drop-shadow(0 0 30px rgba(255, 215, 0, 0.3)) drop-shadow(0 2px 8px rgba(184, 134, 11, 0.4));\\n    position: relative;\\n  }\\n\\n\\n\\n  .text-glass {\\n    color: rgba(255, 255, 255, 0.9);\\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  /* Enhanced Arabic typography */\\n  .text-arabic {\\n    @apply font-cairo;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n\\n  .text-arabic-display {\\n    @apply font-cairo;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1;\\n    font-weight: 700;\\n  }\\n\\n  .text-arabic-premium {\\n    @apply font-cairo;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n    color: var(--text-primary);\\n    text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);\\n    letter-spacing: 0.025em;\\n    line-height: 1.3;\\n    font-weight: 700;\\n  }\\n\\n  /* Premium section headers with Syrian cultural elements */\\n  .section-header-premium {\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%);\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\\n    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\\n  }\\n\\n  /* Gold accent elements */\\n  .gold-accent-vertical {\\n    background: linear-gradient(180deg, #FFD700 0%, #B8860B 100%);\\n    border-radius: 9999px;\\n    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);\\n  }\\n\\n  /* Premium glass morphism cards */\\n  .glass-card-premium {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    border-radius: 20px;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    transition: all 0.3s ease-in-out;\\n  }\\n\\n  .glass-card-premium:hover {\\n    transform: translateY(-8px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n  }\\n\\n  /* Gold gradient buttons */\\n  .btn-gold-gradient {\\n    background: linear-gradient(135deg, #FFD700 0%, #B8860B 100%);\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);\\n    color: white;\\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .btn-gold-gradient:hover {\\n    transform: translateY(-2px) scale(1.02);\\n    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);\\n  }\\n\\n  /* Shimmer effect for buttons and cards */\\n  .shimmer-effect {\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);\\n    animation: glassShimmer 1.5s ease-in-out infinite;\\n  }\\n\\n  /* Popular badge styling */\\n  .popular-badge {\\n    background: linear-gradient(135deg, #FFD700 0%, #B8860B 100%);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);\\n    border-radius: 25px;\\n  }\\n\\n  /* Tab navigation premium styling */\\n  .tab-navigation-premium {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    border-radius: 16px;\\n  }\\n\\n  /* Premium Gold/Metallic Buttons */\\n  .btn-gold-premium {\\n    background: linear-gradient(135deg,\\n      #8B6914 0%,\\n      #B8860B 15%,\\n      #DAA520 30%,\\n      #FFD700 45%,\\n      #FFED4E 60%,\\n      #FFD700 75%,\\n      #DAA520 90%,\\n      #B8860B 100%);\\n    background-size: 300% 300%;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 2px solid rgba(255, 215, 0, 0.4);\\n    box-shadow:\\n      0 8px 25px rgba(255, 215, 0, 0.4),\\n      0 4px 15px rgba(184, 134, 11, 0.3),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.3),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.2);\\n    color: #1a1a1a;\\n    font-weight: 700;\\n    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.4);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n    animation: goldShine 4s ease-in-out infinite;\\n  }\\n\\n  .btn-gold-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);\\n    transition: left 0.6s ease;\\n  }\\n\\n  .btn-gold-premium:hover::before {\\n    left: 100%;\\n  }\\n\\n  .btn-gold-premium:hover {\\n    transform: translateY(-3px) scale(1.05);\\n    box-shadow:\\n      0 15px 40px rgba(255, 215, 0, 0.5),\\n      0 8px 25px rgba(184, 134, 11, 0.4),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.4),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.3);\\n    background-position: 100% 100%;\\n    filter: brightness(1.1) saturate(1.1);\\n    border-color: rgba(255, 215, 0, 0.6);\\n  }\\n\\n  /* Metallic Card Effects */\\n  .card-metallic-gold {\\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 215, 0, 0.2);\\n    border-radius: 20px;\\n    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .card-metallic-gold::before {\\n    content: '';\\n    position: absolute;\\n    top: -50%;\\n    left: -50%;\\n    width: 200%;\\n    height: 200%;\\n    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);\\n    animation: goldGlow 4s ease-in-out infinite;\\n    pointer-events: none;\\n  }\\n\\n  .card-metallic-gold:hover {\\n    transform: translateY(-8px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n    border-color: rgba(255, 215, 0, 0.4);\\n  }\\n  \\n  /* Animation utilities */\\n  .animate-fade-in-up {\\n    animation: fadeInUp 0.6s ease-out forwards;\\n  }\\n  \\n  .animate-fade-in-down {\\n    animation: fadeInDown 0.6s ease-out forwards;\\n  }\\n  \\n  .animate-fade-in-left {\\n    animation: fadeInLeft 0.6s ease-out forwards;\\n  }\\n  \\n  .animate-fade-in-right {\\n    animation: fadeInRight 0.6s ease-out forwards;\\n  }\\n}\\n\\n/* Utility classes */\\n@layer utilities {\\n  /* Text direction utilities */\\n  .text-start {\\n    text-align: start;\\n  }\\n  \\n  .text-end {\\n    text-align: end;\\n  }\\n  \\n  /* Margin utilities for RTL */\\n  .ms-auto {\\n    margin-inline-start: auto;\\n  }\\n  \\n  .me-auto {\\n    margin-inline-end: auto;\\n  }\\n  \\n  /* Padding utilities for RTL */\\n  .ps-4 {\\n    padding-inline-start: 1rem;\\n  }\\n  \\n  .pe-4 {\\n    padding-inline-end: 1rem;\\n  }\\n}\\n\\n/* Keyframe animations */\\n@keyframes fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInLeft {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes fadeInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n/* Enhanced keyframe animations for glass effects */\\n@keyframes gradientShift {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGradient {\\n  0%, 100% { background-position: 0% 0%; }\\n  25% { background-position: 100% 0%; }\\n  50% { background-position: 100% 100%; }\\n  75% { background-position: 0% 100%; }\\n}\\n\\n@keyframes glassShimmer {\\n  0% { background-position: -200% 0; }\\n  100% { background-position: 200% 0; }\\n}\\n\\n@keyframes floatGlass {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n    filter: blur(0px);\\n  }\\n  25% {\\n    transform: translateY(-10px) rotate(1deg);\\n    filter: blur(0.5px);\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(0deg);\\n    filter: blur(1px);\\n  }\\n  75% {\\n    transform: translateY(-10px) rotate(-1deg);\\n    filter: blur(0.5px);\\n  }\\n}\\n\\n@keyframes pulseGlass {\\n  0%, 100% {\\n    background: rgba(255, 255, 255, 0.1);\\n    border-color: rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n  50% {\\n    background: rgba(255, 255, 255, 0.15);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n/* Premium Gold/Metallic Animations */\\n@keyframes goldShine {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGoldShine {\\n  0% { background-position: -200% 0%; }\\n  50% { background-position: 0% 0%; }\\n  100% { background-position: 200% 0%; }\\n}\\n\\n@keyframes goldGlimmer {\\n  0% { transform: translateX(-100%) skewX(-15deg); }\\n  100% { transform: translateX(200%) skewX(-15deg); }\\n}\\n\\n@keyframes textShimmer {\\n  0% {\\n    background-position: -200% 0;\\n    opacity: 0;\\n  }\\n  50% {\\n    background-position: 0% 0;\\n    opacity: 1;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n    opacity: 0;\\n  }\\n}\\n\\n@keyframes textShimmerEnhanced {\\n  0% {\\n    background-position: -200% 0;\\n    opacity: 0;\\n  }\\n  10% {\\n    opacity: 0.3;\\n  }\\n  50% {\\n    background-position: 0% 0;\\n    opacity: 1;\\n  }\\n  90% {\\n    opacity: 0.3;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n    opacity: 0;\\n  }\\n}\\n\\n@keyframes goldGlow {\\n  0%, 100% {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: rotate(180deg) scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n\\n/* Reduced motion preferences */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n\\n  .glass,\\n  .glass-card,\\n  .glass-button {\\n    background: white !important;\\n    backdrop-filter: none !important;\\n    -webkit-backdrop-filter: none !important;\\n    border: 1px solid #ccc !important;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ })

});