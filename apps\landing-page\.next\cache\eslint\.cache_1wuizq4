[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx": "19"}, {"size": 14710, "mtime": 1749600836091, "results": "20", "hashOfConfig": "21"}, {"size": 9016, "mtime": 1749596942009, "results": "22", "hashOfConfig": "21"}, {"size": 385, "mtime": 1749589496137, "results": "23", "hashOfConfig": "21"}, {"size": 12920, "mtime": 1749603218618, "results": "24", "hashOfConfig": "21"}, {"size": 21668, "mtime": 1749600046512, "results": "25", "hashOfConfig": "21"}, {"size": 13033, "mtime": 1749607885282, "results": "26", "hashOfConfig": "21"}, {"size": 14998, "mtime": 1749608544155, "results": "27", "hashOfConfig": "21"}, {"size": 15557, "mtime": 1749607867202, "results": "28", "hashOfConfig": "21"}, {"size": 15978, "mtime": 1749600583678, "results": "29", "hashOfConfig": "21"}, {"size": 18603, "mtime": 1749607796602, "results": "30", "hashOfConfig": "21"}, {"size": 14612, "mtime": 1749607928375, "results": "31", "hashOfConfig": "21"}, {"size": 3319, "mtime": 1749593558895, "results": "32", "hashOfConfig": "21"}, {"size": 2575, "mtime": 1749598342857, "results": "33", "hashOfConfig": "21"}, {"size": 1646, "mtime": 1749589446276, "results": "34", "hashOfConfig": "21"}, {"size": 2677, "mtime": 1749597314087, "results": "35", "hashOfConfig": "21"}, {"size": 2428, "mtime": 1749596227387, "results": "36", "hashOfConfig": "21"}, {"size": 989, "mtime": 1749596212722, "results": "37", "hashOfConfig": "21"}, {"size": 2120, "mtime": 1749597019188, "results": "38", "hashOfConfig": "21"}, {"size": 2328, "mtime": 1749607729735, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ody2rz", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["97"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["98", "99", "100", "101", "102", "103", "104", "105"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx", [], [], {"ruleId": "106", "severity": 1, "message": "107", "line": 37, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 37, "endColumn": 20, "suggestions": "110", "suppressions": "111"}, {"ruleId": "106", "severity": 1, "message": "107", "line": 7, "column": 30, "nodeType": "108", "messageId": "109", "endLine": 7, "endColumn": 43}, {"ruleId": "106", "severity": 1, "message": "107", "line": 8, "column": 29, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 41}, {"ruleId": "106", "severity": 1, "message": "107", "line": 37, "column": 5, "nodeType": "108", "messageId": "109", "endLine": 37, "endColumn": 18}, {"ruleId": "112", "severity": 1, "message": "113", "line": 37, "column": 31, "nodeType": "114", "messageId": "115", "endLine": 37, "endColumn": 34, "suggestions": "116"}, {"ruleId": "106", "severity": 1, "message": "107", "line": 44, "column": 5, "nodeType": "108", "messageId": "109", "endLine": 44, "endColumn": 17}, {"ruleId": "112", "severity": 1, "message": "113", "line": 44, "column": 30, "nodeType": "114", "messageId": "115", "endLine": 44, "endColumn": 33, "suggestions": "117"}, {"ruleId": "106", "severity": 1, "message": "107", "line": 73, "column": 3, "nodeType": "108", "messageId": "109", "endLine": 73, "endColumn": 16}, {"ruleId": "106", "severity": 1, "message": "107", "line": 74, "column": 3, "nodeType": "108", "messageId": "109", "endLine": 74, "endColumn": 15}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["118"], ["119"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["120", "121"], ["122", "123"], {"messageId": "124", "data": "125", "fix": "126", "desc": "127"}, {"kind": "128", "justification": "129"}, {"messageId": "130", "fix": "131", "desc": "132"}, {"messageId": "133", "fix": "134", "desc": "135"}, {"messageId": "130", "fix": "136", "desc": "132"}, {"messageId": "133", "fix": "137", "desc": "135"}, "removeConsole", {"propertyName": "138"}, {"range": "139", "text": "129"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "140", "text": "141"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "142", "text": "143"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "144", "text": "141"}, {"range": "145", "text": "143"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289]]