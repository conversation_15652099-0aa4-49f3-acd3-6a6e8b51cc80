import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  DocumentPlusIcon,
  InboxArrowDownIcon,
  UserIcon,
  PlayIcon,
  UserPlusIcon,
  MagnifyingGlassIcon,
  PaperAirplaneIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';

export default function HowItWorks() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [activeTab, setActiveTab] = useState<'clients' | 'experts'>('clients');

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Steps for clients
  const clientSteps = [
    {
      title: t('howItWorks.forClients.steps.0.title'),
      description: t('howItWorks.forClients.steps.0.description'),
      icon: DocumentPlusIcon,
    },
    {
      title: t('howItWorks.forClients.steps.1.title'),
      description: t('howItWorks.forClients.steps.1.description'),
      icon: InboxArrowDownIcon,
    },
    {
      title: t('howItWorks.forClients.steps.2.title'),
      description: t('howItWorks.forClients.steps.2.description'),
      icon: UserIcon,
    },
    {
      title: t('howItWorks.forClients.steps.3.title'),
      description: t('howItWorks.forClients.steps.3.description'),
      icon: PlayIcon,
    },
  ];

  // Steps for experts
  const expertSteps = [
    {
      title: t('howItWorks.forExperts.steps.0.title'),
      description: t('howItWorks.forExperts.steps.0.description'),
      icon: UserPlusIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.1.title'),
      description: t('howItWorks.forExperts.steps.1.description'),
      icon: MagnifyingGlassIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.2.title'),
      description: t('howItWorks.forExperts.steps.2.description'),
      icon: PaperAirplaneIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.3.title'),
      description: t('howItWorks.forExperts.steps.3.description'),
      icon: BanknotesIcon,
    },
  ];

  const currentSteps = activeTab === 'clients' ? clientSteps : expertSteps;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="how-it-works"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(2, 132, 199, 0.08) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Glass Orbs */}
        <motion.div
          animate={{
            y: [-22, 22, -22],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 19, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-24 right-16 w-30 h-30 rounded-full opacity-12"
          style={{
            background: 'rgba(2, 132, 199, 0.1)',
            backdropFilter: 'blur(22px)',
            WebkitBackdropFilter: 'blur(22px)',
            border: '1px solid rgba(2, 132, 199, 0.2)',
            boxShadow: '0 8px 32px rgba(2, 132, 199, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [28, -28, 28],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 21, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-36 left-14 w-26 h-26 rounded-full opacity-10"
          style={{
            background: 'rgba(124, 58, 237, 0.1)',
            backdropFilter: 'blur(18px)',
            WebkitBackdropFilter: 'blur(18px)',
            border: '1px solid rgba(124, 58, 237, 0.2)',
            boxShadow: '0 8px 32px rgba(124, 58, 237, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-12, 12, -12],
              x: [-6, 6, -6],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 5 + i * 1.2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.8
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${20 + (i * 12)}%`,
              top: `${30 + (i * 10)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-16"
        >
          {/* Enhanced Section Header with Premium Typography */}
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >


            <motion.h2
              variants={itemVariants}
              className={`heading-lg mb-4 text-arabic-premium ${
                isRTL
                  ? 'font-cairo text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight'
                  : 'font-display text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight'
              }`}
              style={{
                background: 'linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))'
              }}
            >
              {t('howItWorks.title')}
            </motion.h2>
          </motion.div>

          <motion.p
            variants={itemVariants}
            className={`text-xl lg:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed ${
              isRTL ? 'font-tajawal' : 'font-sans'
            }`}
            style={{
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
            }}
          >
            {t('howItWorks.subtitle')}
          </motion.p>
        </motion.div>

        {/* Enhanced Tab Navigation */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="flex justify-center mb-12"
        >
          <div
            className="relative p-2 rounded-2xl overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
              backdropFilter: 'blur(25px)',
              WebkitBackdropFilter: 'blur(25px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* Gold Accent */}
            <div className="absolute top-1 right-1 flex space-x-1 rtl:space-x-reverse opacity-30">
              <div className="w-1 h-4 bg-gold-500 rounded-full"></div>
              <div className="w-1 h-4 bg-gold-600 rounded-full"></div>
            </div>

            <motion.button
              type="button"
              onClick={() => setActiveTab('clients')}
              className={`relative px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                isRTL ? 'font-cairo' : 'font-sans'
              } ${
                activeTab === 'clients'
                  ? 'text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
              style={{
                background: activeTab === 'clients'
                  ? 'linear-gradient(135deg, #FFD700 0%, #B8860B 100%)'
                  : 'transparent',
                boxShadow: activeTab === 'clients'
                  ? '0 4px 15px rgba(255, 215, 0, 0.3)'
                  : 'none'
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('howItWorks.forClients.title')}
            </motion.button>
            <motion.button
              type="button"
              onClick={() => setActiveTab('experts')}
              className={`relative px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                isRTL ? 'font-cairo' : 'font-sans'
              } ${
                activeTab === 'experts'
                  ? 'text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
              style={{
                background: activeTab === 'experts'
                  ? 'linear-gradient(135deg, #FFD700 0%, #B8860B 100%)'
                  : 'transparent',
                boxShadow: activeTab === 'experts'
                  ? '0 4px 15px rgba(255, 215, 0, 0.3)'
                  : 'none'
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('howItWorks.forExperts.title')}
            </motion.button>
          </div>
        </motion.div>

        {/* Steps */}
        <motion.div
          key={activeTab}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {currentSteps.map((step, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="relative"
            >

              
              <div
                className="relative p-8 text-center h-full rounded-2xl overflow-hidden group"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%)',
                  backdropFilter: 'blur(25px)',
                  WebkitBackdropFilter: 'blur(25px)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease-in-out'
                }}
              >
                {/* Enhanced Step Number with Gold Colors */}
                <motion.div
                  className="relative w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center text-white font-bold text-2xl overflow-hidden"
                  style={{
                    background: `linear-gradient(135deg, ${index % 2 === 0 ? '#FFD700' : '#B8860B'} 0%, ${index % 2 === 0 ? '#B8860B' : '#FFD700'} 100%)`,
                    boxShadow: '0 8px 25px rgba(255, 215, 0, 0.3)'
                  }}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className={`relative z-10 ${isRTL ? 'font-cairo' : 'font-display'}`}>
                    {index + 1}
                  </span>
                  {/* Shimmer effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                      animation: 'glassShimmer 1.5s ease-in-out infinite'
                    }}
                  />
                </motion.div>

                {/* Enhanced Icon */}
                <motion.div
                  className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center relative overflow-hidden"
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <step.icon className="w-8 h-8 text-white relative z-10" />
                </motion.div>

                {/* Enhanced Content */}
                <motion.h3
                  className={`heading-sm mb-4 text-white font-bold ${
                    isRTL ? 'font-cairo text-xl lg:text-2xl' : 'font-display text-xl lg:text-2xl'
                  }`}
                  style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
                >
                  {step.title}
                </motion.h3>
                <p className={`text-gray-300 leading-relaxed ${
                  isRTL ? 'font-tajawal' : 'font-sans'
                }`}>
                  {step.description}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced CTA */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mt-20"
        >
          <motion.button
            type="button"
            className={`relative px-12 py-5 text-xl font-semibold rounded-2xl overflow-hidden group ${
              isRTL ? 'font-cairo' : 'font-display'
            }`}
            style={{
              background: 'linear-gradient(135deg, #FFD700 0%, #B8860B 100%)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 12px 35px rgba(255, 215, 0, 0.4)',
              color: 'white',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
            }}
            whileHover={{
              scale: 1.05,
              y: -3,
              transition: { duration: 0.2 }
            }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Button shimmer effect */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                animation: 'glassShimmer 1.5s ease-in-out infinite'
              }}
            />
            <span className="relative z-10">
              {activeTab === 'clients'
                ? (isRTL ? 'انشر مشروعك الآن' : 'Post Your Project Now')
                : (isRTL ? 'انضم كخبير' : 'Join as Expert')
              }
            </span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
