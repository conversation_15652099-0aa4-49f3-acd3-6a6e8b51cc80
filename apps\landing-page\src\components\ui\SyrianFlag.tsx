import React from 'react';

interface SyrianFlagProps {
  width?: number;
  height?: number;
  className?: string;
}

export default function SyrianFlag({
  width = 120,
  height = 80,
  className = ''
}: SyrianFlagProps) {
  return (
    <div className={`inline-block ${className}`}>
      <svg
        width={width}
        height={height}
        viewBox="0 0 120 80"
        xmlns="http://www.w3.org/2000/svg"
        className="drop-shadow-lg"
      >
        {/* Flag background */}
        <defs>
          {/* Subtle gradient for depth */}
          <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#CE1126" />
            <stop offset="50%" stopColor="#E01B2F" />
            <stop offset="100%" stopColor="#CE1126" />
          </linearGradient>

          <linearGradient id="whiteGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#FFFFFF" />
            <stop offset="50%" stopColor="#F8F9FA" />
            <stop offset="100%" stopColor="#FFFFFF" />
          </linearGradient>

          <linearGradient id="blackGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#000000" />
            <stop offset="50%" stopColor="#1A1A1A" />
            <stop offset="100%" stopColor="#000000" />
          </linearGradient>

          {/* Shadow filter */}
          <filter id="flagShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" floodOpacity="0.3"/>
          </filter>
        </defs>

        {/* Current Syrian Flag - Three horizontal stripes */}
        <g filter="url(#flagShadow)">
          {/* Red stripe (top) */}
          <rect x="0" y="0" width="120" height="26.67" fill="url(#redGradient)" />

          {/* White stripe (middle) */}
          <rect x="0" y="26.67" width="120" height="26.67" fill="url(#whiteGradient)" />

          {/* Black stripe (bottom) */}
          <rect x="0" y="53.33" width="120" height="26.67" fill="url(#blackGradient)" />
        </g>

        {/* Subtle border */}
        <rect
          x="0.5"
          y="0.5"
          width="119"
          height="79"
          fill="none"
          stroke="rgba(0,0,0,0.1)"
          strokeWidth="1"
        />
      </svg>
    </div>
  );
}
